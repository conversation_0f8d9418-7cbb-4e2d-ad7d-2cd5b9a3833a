# 技术方案设计

## 架构概述

为 aigw CLI 添加 install/uninstall 功能，采用模块化设计，包含前置条件检查、安装流程管理、输出美化等模块。

## 技术栈

- **语言**: Go
- **CLI 框架**: Kong (已有)
- **Kubernetes 客户端**: client-go
- **Helm 客户端**: helm.sh/helm/v3
- **输出美化**: 
  - github.com/fatih/color (颜色输出)
  - github.com/briandowns/spinner (进度指示器)
  - github.com/schollz/progressbar/v3 (进度条)

## 模块设计

### 1. 命令结构

```go
type cmdInstall struct {
    Namespace string `help:"Target namespace for installation" default:"envoy-ai-gateway-system"`
    Version   string `help:"Version to install" default:"v0.0.0-latest"`
    SkipCRDs  bool   `help:"Skip CRDs installation"`
    DryRun    bool   `help:"Show what would be done without executing"`
    Force     bool   `help:"Skip confirmation prompts"`
    Debug     bool   `help:"Enable debug logging"`
}

type cmdUninstall struct {
    Namespace string `help:"Target namespace for uninstallation" default:"envoy-ai-gateway-system"`
    Force     bool   `help:"Skip confirmation prompts"`
    Debug     bool   `help:"Enable debug logging"`
}
```

### 2. 核心模块

#### Prerequisites Checker
- 检查 kubectl, helm, curl 是否安装
- 验证 Kubernetes 集群连接和版本
- 检查权限

#### Installation Manager
- 管理安装步骤的执行顺序
- 处理错误和回滚
- 状态跟踪

#### UI Manager
- 统一的输出格式化
- 进度指示器
- 颜色和符号管理

### 3. 安装流程

```mermaid
graph TD
    A[开始安装] --> B[检查前置条件]
    B --> C{前置条件OK?}
    C -->|否| D[显示错误并退出]
    C -->|是| E[验证K8s集群]
    E --> F{集群版本OK?}
    F -->|否| G[显示版本错误并退出]
    F -->|是| H[安装Envoy Gateway]
    H --> I[安装AI Gateway]
    I --> J[配置Envoy Gateway]
    J --> K[验证安装]
    K --> L[显示成功信息]
```

### 4. 文件结构

```
cmd/aigw/
├── main.go                 # 主入口，添加install/uninstall命令
├── install.go              # install命令实现
├── uninstall.go            # uninstall命令实现
└── internal/
    ├── installer/
    │   ├── prerequisites.go # 前置条件检查
    │   ├── manager.go       # 安装管理器
    │   ├── envoy_gateway.go # Envoy Gateway安装
    │   ├── ai_gateway.go    # AI Gateway安装
    │   └── config.go        # 配置管理
    ├── ui/
    │   ├── output.go        # 输出格式化
    │   ├── progress.go      # 进度指示器
    │   └── colors.go        # 颜色管理
    └── k8s/
        ├── client.go        # Kubernetes客户端
        └── helm.go          # Helm客户端
```

## 安全性考虑

1. **权限验证**: 在执行安装前验证用户是否有足够的 Kubernetes 权限
2. **输入验证**: 验证用户输入的命名空间、版本等参数
3. **错误处理**: 安全地处理敏感信息，避免在日志中泄露

## 测试策略

1. **单元测试**: 对各个模块进行单元测试
2. **集成测试**: 测试完整的安装/卸载流程
3. **Mock测试**: 使用mock Kubernetes和Helm客户端进行测试
4. **E2E测试**: 在真实环境中测试安装/卸载功能

## 错误处理和回滚

1. **分步验证**: 每个步骤完成后验证结果
2. **自动回滚**: 安装失败时自动清理已安装的组件
3. **手动回滚**: 提供手动回滚选项和指导

## 性能考虑

1. **并发安装**: 在可能的情况下并行执行安装步骤
2. **缓存**: 缓存Kubernetes客户端和集群信息
3. **超时控制**: 为每个操作设置合理的超时时间
