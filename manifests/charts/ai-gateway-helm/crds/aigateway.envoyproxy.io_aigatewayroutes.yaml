# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.3
  name: aigatewayroutes.aigateway.envoyproxy.io
spec:
  group: aigateway.envoyproxy.io
  names:
    kind: AIGatewayRoute
    listKind: AIGatewayRouteList
    plural: aigatewayroutes
    singular: aigatewayroute
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[-1:].type
      name: Status
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          AIGatewayRoute combines multiple AIServiceBackends and attaching them to Gateway(s) resources.

          This serves as a way to define a "unified" AI API for a Gateway which allows downstream
          clients to use a single schema API to interact with multiple AI backends.

          The schema field is used to determine the structure of the requests that the Gateway will
          receive. And then the Gateway will route the traffic to the appropriate AIServiceBackend based
          on the output schema of the AIServiceBackend while doing the other necessary jobs like
          upstream authentication, rate limit, etc.

          Envoy AI Gateway will generate the following k8s resources corresponding to the AIGatewayRoute:

            - HTTPRoute of the Gateway API as a top-level resource to bind all backends.
              The name of the HTTPRoute is the same as the AIGatewayRoute.
            - EnvoyExtensionPolicy of the Envoy Gateway API to attach the AI Gateway filter into the target Gateways.
              This will be created per Gateway, and its name is `ai-eg-eep-${gateway-name}`.
            - HTTPRouteFilter of the Envoy Gateway API per namespace for automatic hostname rewrite.
              The name of the HTTPRouteFilter is `ai-eg-host-rewrite`.

          All of these resources are created in the same namespace as the AIGatewayRoute. Note that this is the implementation
          detail subject to change. If you want to customize the default behavior of the Envoy AI Gateway, you can use these
          resources as a reference and create your own resources. Alternatively, you can use EnvoyPatchPolicy API of the Envoy
          Gateway to patch the generated resources. For example, you can configure the retry fallback behavior by attaching
          BackendTrafficPolicy API of Envoy Gateway to the generated HTTPRoute.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the details of the AIGatewayRoute.
            properties:
              filterConfig:
                description: |-
                  FilterConfig is the configuration for the AI Gateway filter inserted in the generated HTTPRoute.

                  An AI Gateway filter is responsible for the transformation of the request and response
                  as well as the routing behavior based on the model name extracted from the request content, etc.

                  Currently, the filter is only implemented as an external processor filter, which might be
                  extended to other types of filters in the future. See https://github.com/envoyproxy/ai-gateway/issues/90
                properties:
                  externalProcessor:
                    description: |-
                      ExternalProcessor is the configuration for the external processor filter.
                      This is optional, and if not set, the default values of Deployment spec will be used.
                    properties:
                      resources:
                        description: |-
                          Resources required by the external processor container.
                          More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/

                          Note: when multiple AIGatewayRoute resources are attached to the same Gateway, and each
                          AIGatewayRoute has a different resource configuration, the ai-gateway will pick one of them
                          to configure the resource requirements of the external processor container.
                        properties:
                          claims:
                            description: |-
                              Claims lists the names of resources, defined in spec.resourceClaims,
                              that are used by this container.

                              This is an alpha field and requires enabling the
                              DynamicResourceAllocation feature gate.

                              This field is immutable. It can only be set for containers.
                            items:
                              description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                              properties:
                                name:
                                  description: |-
                                    Name must match the name of one entry in pod.spec.resourceClaims of
                                    the Pod where this field is used. It makes that resource available
                                    inside a container.
                                  type: string
                                request:
                                  description: |-
                                    Request is the name chosen for a request in the referenced claim.
                                    If empty, everything from the claim is made available, otherwise
                                    only the result of this request.
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          limits:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Limits describes the maximum amount of compute resources allowed.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                          requests:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            description: |-
                              Requests describes the minimum amount of compute resources required.
                              If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                              otherwise to an implementation-defined value. Requests cannot exceed Limits.
                              More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                            type: object
                        type: object
                    type: object
                  type:
                    default: ExternalProcessor
                    description: |-
                      Type specifies the type of the filter configuration.

                      Currently, only ExternalProcessor is supported, and default is ExternalProcessor.
                    enum:
                    - ExternalProcessor
                    - DynamicModule
                    type: string
                required:
                - type
                type: object
              llmRequestCosts:
                description: "LLMRequestCosts specifies how to capture the cost of
                  the LLM-related request, notably the token usage.\nThe AI Gateway
                  filter will capture each specified number and store it in the Envoy's
                  dynamic\nmetadata per HTTP request. The namespaced key is \"io.envoy.ai_gateway\",\n\nFor
                  example, let's say we have the following LLMRequestCosts configuration:\n```yaml\n\tllmRequestCosts:\n\t-
                  metadataKey: llm_input_token\n\t  type: InputToken\n\t- metadataKey:
                  llm_output_token\n\t  type: OutputToken\n\t- metadataKey: llm_total_token\n\t
                  \ type: TotalToken\n```\nThen, with the following BackendTrafficPolicy
                  of Envoy Gateway, you can have three\nrate limit buckets for each
                  unique x-user-id header value. One bucket is for the input token,\nthe
                  other is for the output token, and the last one is for the total
                  token.\nEach bucket will be reduced by the corresponding token usage
                  captured by the AI Gateway filter.\n\n```yaml\n\tapiVersion: gateway.envoyproxy.io/v1alpha1\n\tkind:
                  BackendTrafficPolicy\n\tmetadata:\n\t  name: some-example-token-rate-limit\n\t
                  \ namespace: default\n\tspec:\n\t  targetRefs:\n\t  - group: gateway.networking.k8s.io\n\t
                  \    kind: HTTPRoute\n\t     name: usage-rate-limit\n\t  rateLimit:\n\t
                  \   type: Global\n\t    global:\n\t      rules:\n\t        - clientSelectors:\n\t
                  \           # Do the rate limiting based on the x-user-id header.\n\t
                  \           - headers:\n\t                - name: x-user-id\n\t
                  \                 type: Distinct\n\t          limit:\n\t            #
                  Configures the number of \"tokens\" allowed per hour.\n\t            requests:
                  10000\n\t            unit: Hour\n\t          cost:\n\t            request:\n\t
                  \             from: Number\n\t              # Setting the request
                  cost to zero allows to only check the rate limit budget,\n\t              #
                  and not consume the budget on the request path.\n\t              number:
                  0\n\t            # This specifies the cost of the response retrieved
                  from the dynamic metadata set by the AI Gateway filter.\n\t            #
                  The extracted value will be used to consume the rate limit budget,
                  and subsequent requests will be rate limited\n\t            # if
                  the budget is exhausted.\n\t            response:\n\t              from:
                  Metadata\n\t              metadata:\n\t                namespace:
                  io.envoy.ai_gateway\n\t                key: llm_input_token\n\t
                  \       - clientSelectors:\n\t            - headers:\n\t                -
                  name: x-user-id\n\t                  type: Distinct\n\t          limit:\n\t
                  \           requests: 10000\n\t            unit: Hour\n\t          cost:\n\t
                  \           request:\n\t              from: Number\n\t              number:
                  0\n\t            response:\n\t              from: Metadata\n\t              metadata:\n\t
                  \               namespace: io.envoy.ai_gateway\n\t                key:
                  llm_output_token\n\t        - clientSelectors:\n\t            -
                  headers:\n\t                - name: x-user-id\n\t                  type:
                  Distinct\n\t          limit:\n\t            requests: 10000\n\t
                  \           unit: Hour\n\t          cost:\n\t            request:\n\t
                  \             from: Number\n\t              number: 0\n\t            response:\n\t
                  \             from: Metadata\n\t              metadata:\n\t                namespace:
                  io.envoy.ai_gateway\n\t                key: llm_total_token\n```\n\nNote
                  that when multiple AIGatewayRoute resources are attached to the
                  same Gateway, and\ndifferent costs are configured for the same metadata
                  key, the ai-gateway will pick one of them\nto configure the metadata
                  key in the generated HTTPRoute, and ignore the rest."
                items:
                  description: LLMRequestCost configures each request cost.
                  properties:
                    cel:
                      description: "CEL is the CEL expression to calculate the cost
                        of the request.\nThe CEL expression must return a signed or
                        unsigned integer. If the\nreturn value is negative, it will
                        be error.\n\nThe expression can use the following variables:\n\n\t*
                        model: the model name extracted from the request content.
                        Type: string.\n\t* backend: the backend name in the form of
                        \"name.namespace\". Type: string.\n\t* input_tokens: the number
                        of input tokens. Type: unsigned integer.\n\t* output_tokens:
                        the number of output tokens. Type: unsigned integer.\n\t*
                        total_tokens: the total number of tokens. Type: unsigned integer.\n\nFor
                        example, the following expressions are valid:\n\n\t* \"model
                        == 'llama' ?  input_tokens + output_token * 0.5 : total_tokens\"\n\t*
                        \"backend == 'foo.default' ?  input_tokens + output_tokens
                        : total_tokens\"\n\t* \"input_tokens + output_tokens + total_tokens\"\n\t*
                        \"input_tokens * output_tokens\""
                      type: string
                    metadataKey:
                      description: MetadataKey is the key of the metadata to store
                        this cost of the request.
                      type: string
                    type:
                      description: |-
                        Type specifies the type of the request cost. The default is "OutputToken",
                        and it uses "output token" as the cost. The other types are "InputToken", "TotalToken",
                        and "CEL".
                      enum:
                      - OutputToken
                      - InputToken
                      - TotalToken
                      - CEL
                      type: string
                  required:
                  - metadataKey
                  - type
                  type: object
                maxItems: 36
                type: array
              parentRefs:
                description: |-
                  ParentRefs are the names of the Gateway resources this AIGatewayRoute is being attached to.
                  Cross namespace references are not supported. In other words, the Gateway resources must be in the
                  same namespace as the AIGatewayRoute. Currently, each reference's Kind must be Gateway.
                items:
                  description: |-
                    ParentReference identifies an API object (usually a Gateway) that can be considered
                    a parent of this resource (usually a route). There are two kinds of parent resources
                    with "Core" support:

                    * Gateway (Gateway conformance profile)
                    * Service (Mesh conformance profile, ClusterIP Services only)

                    This API may be extended in the future to support additional kinds of parent
                    resources.

                    The API object must be valid in the cluster; the Group and Kind must
                    be registered in the cluster for this reference to be valid.
                  properties:
                    group:
                      default: gateway.networking.k8s.io
                      description: |-
                        Group is the group of the referent.
                        When unspecified, "gateway.networking.k8s.io" is inferred.
                        To set the core API group (such as for a "Service" kind referent),
                        Group must be explicitly set to "" (empty string).

                        Support: Core
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      default: Gateway
                      description: |-
                        Kind is kind of the referent.

                        There are two kinds of parent resources with "Core" support:

                        * Gateway (Gateway conformance profile)
                        * Service (Mesh conformance profile, ClusterIP Services only)

                        Support for other resources is Implementation-Specific.
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      description: |-
                        Name is the name of the referent.

                        Support: Core
                      maxLength: 253
                      minLength: 1
                      type: string
                    namespace:
                      description: |-
                        Namespace is the namespace of the referent. When unspecified, this refers
                        to the local namespace of the Route.

                        Note that there are specific rules for ParentRefs which cross namespace
                        boundaries. Cross-namespace references are only valid if they are explicitly
                        allowed by something in the namespace they are referring to. For example:
                        Gateway has the AllowedRoutes field, and ReferenceGrant provides a
                        generic way to enable any other kind of cross-namespace reference.

                        <gateway:experimental:description>
                        ParentRefs from a Route to a Service in the same namespace are "producer"
                        routes, which apply default routing rules to inbound connections from
                        any namespace to the Service.

                        ParentRefs from a Route to a Service in a different namespace are
                        "consumer" routes, and these routing rules are only applied to outbound
                        connections originating from the same namespace as the Route, for which
                        the intended destination of the connections are a Service targeted as a
                        ParentRef of the Route.
                        </gateway:experimental:description>

                        Support: Core
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                      type: string
                    port:
                      description: |-
                        Port is the network port this Route targets. It can be interpreted
                        differently based on the type of parent resource.

                        When the parent resource is a Gateway, this targets all listeners
                        listening on the specified port that also support this kind of Route(and
                        select this Route). It's not recommended to set `Port` unless the
                        networking behaviors specified in a Route must apply to a specific port
                        as opposed to a listener(s) whose port(s) may be changed. When both Port
                        and SectionName are specified, the name and port of the selected listener
                        must match both specified values.

                        <gateway:experimental:description>
                        When the parent resource is a Service, this targets a specific port in the
                        Service spec. When both Port (experimental) and SectionName are specified,
                        the name and port of the selected port must match both specified values.
                        </gateway:experimental:description>

                        Implementations MAY choose to support other parent resources.
                        Implementations supporting other types of parent resources MUST clearly
                        document how/if Port is interpreted.

                        For the purpose of status, an attachment is considered successful as
                        long as the parent resource accepts it partially. For example, Gateway
                        listeners can restrict which Routes can attach to them by Route kind,
                        namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
                        from the referencing Route, the Route MUST be considered successfully
                        attached. If no Gateway listeners accept attachment from this Route,
                        the Route MUST be considered detached from the Gateway.

                        Support: Extended
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                    sectionName:
                      description: |-
                        SectionName is the name of a section within the target resource. In the
                        following resources, SectionName is interpreted as the following:

                        * Gateway: Listener name. When both Port (experimental) and SectionName
                        are specified, the name and port of the selected listener must match
                        both specified values.
                        * Service: Port name. When both Port (experimental) and SectionName
                        are specified, the name and port of the selected listener must match
                        both specified values.

                        Implementations MAY choose to support attaching Routes to other resources.
                        If that is the case, they MUST clearly document how SectionName is
                        interpreted.

                        When unspecified (empty string), this will reference the entire resource.
                        For the purpose of status, an attachment is considered successful if at
                        least one section in the parent resource accepts it. For example, Gateway
                        listeners can restrict which Routes can attach to them by Route kind,
                        namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
                        the referencing Route, the Route MUST be considered successfully
                        attached. If no Gateway listeners accept attachment from this Route, the
                        Route MUST be considered detached from the Gateway.

                        Support: Core
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                  required:
                  - name
                  type: object
                maxItems: 16
                type: array
                x-kubernetes-validations:
                - message: only Gateway is supported
                  rule: self.all(match, match.kind == 'Gateway')
              rules:
                description: |-
                  Rules is the list of AIGatewayRouteRule that this AIGatewayRoute will match the traffic to.
                  Each rule is a subset of the HTTPRoute in the Gateway API (https://gateway-api.sigs.k8s.io/api-types/httproute/).

                  AI Gateway controller will generate a HTTPRoute based on the configuration given here with the additional
                  modifications to achieve the necessary jobs, notably inserting the AI Gateway filter responsible for
                  the transformation of the request and response, etc.

                  In the matching conditions in the AIGatewayRouteRule, `x-ai-eg-model` header is available
                  if we want to describe the routing behavior based on the model name. The model name is extracted
                  from the request content before the routing decision.

                  How multiple rules are matched is the same as the Gateway API. See for the details:
                  https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPRoute
                items:
                  description: AIGatewayRouteRule is a rule that defines the routing
                    behavior of the AIGatewayRoute.
                  properties:
                    backendRefs:
                      description: |-
                        BackendRefs is the list of AIServiceBackend that this rule will route the traffic to.
                        Each backend can have a weight that determines the traffic distribution.

                        The namespace of each backend is "local", i.e. the same namespace as the AIGatewayRoute.

                        By configuring multiple backends, you can achieve the fallback behavior in the case of
                        the primary backend is not available combined with the BackendTrafficPolicy of Envoy Gateway.
                        Please refer to https://gateway.envoyproxy.io/docs/tasks/traffic/failover/ as well as
                        https://gateway.envoyproxy.io/docs/tasks/traffic/retry/.
                      items:
                        description: AIGatewayRouteRuleBackendRef is a reference to
                          a backend with a weight.
                        properties:
                          modelNameOverride:
                            description: Name of the model in the backend. If provided
                              this will override the name provided in the request.
                            type: string
                          name:
                            description: Name is the name of the AIServiceBackend.
                            minLength: 1
                            type: string
                          priority:
                            default: 0
                            description: |-
                              Priority is the priority of the AIServiceBackend. This sets the priority on the underlying endpoints.
                              See: https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/priority
                              Note: This will override the `faillback` property of the underlying Envoy Gateway Backend

                              Default is 0.
                            format: int32
                            minimum: 0
                            type: integer
                          weight:
                            default: 1
                            description: |-
                              Weight is the weight of the AIServiceBackend. This is exactly the same as the weight in
                              the BackendRef in the Gateway API. See for the details:
                              https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.BackendRef

                              Default is 1.
                            format: int32
                            minimum: 0
                            type: integer
                        required:
                        - name
                        type: object
                      maxItems: 128
                      type: array
                    matches:
                      description: |-
                        Matches is the list of AIGatewayRouteMatch that this rule will match the traffic to.
                        This is a subset of the HTTPRouteMatch in the Gateway API. See for the details:
                        https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPRouteMatch
                      items:
                        properties:
                          headers:
                            description: |-
                              Headers specifies HTTP request header matchers. See HeaderMatch in the Gateway API for the details:
                              https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPHeaderMatch
                            items:
                              description: |-
                                HTTPHeaderMatch describes how to select a HTTP route by matching HTTP request
                                headers.
                              properties:
                                name:
                                  description: |-
                                    Name is the name of the HTTP Header to be matched. Name matching MUST be
                                    case-insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).

                                    If multiple entries specify equivalent header names, only the first
                                    entry with an equivalent name MUST be considered for a match. Subsequent
                                    entries with an equivalent header name MUST be ignored. Due to the
                                    case-insensitivity of header names, "foo" and "Foo" are considered
                                    equivalent.

                                    When a header is repeated in an HTTP request, it is
                                    implementation-specific behavior as to how this is represented.
                                    Generally, proxies should follow the guidance from the RFC:
                                    https://www.rfc-editor.org/rfc/rfc7230.html#section-3.2.2 regarding
                                    processing a repeated header, with special handling for "Set-Cookie".
                                  maxLength: 256
                                  minLength: 1
                                  pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                  type: string
                                type:
                                  default: Exact
                                  description: |-
                                    Type specifies how to match against the value of the header.

                                    Support: Core (Exact)

                                    Support: Implementation-specific (RegularExpression)

                                    Since RegularExpression HeaderMatchType has implementation-specific
                                    conformance, implementations can support POSIX, PCRE or any other dialects
                                    of regular expressions. Please read the implementation's documentation to
                                    determine the supported dialect.
                                  enum:
                                  - Exact
                                  - RegularExpression
                                  type: string
                                value:
                                  description: Value is the value of HTTP Header to
                                    be matched.
                                  maxLength: 4096
                                  minLength: 1
                                  type: string
                              required:
                              - name
                              - value
                              type: object
                            maxItems: 16
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                        type: object
                      maxItems: 128
                      type: array
                    modelsCreatedAt:
                      description: |-
                        ModelsCreatedAt represents the creation timestamp of the running models serving by the backends,
                        which will be exported as the field of "Created" in openai-compatible API "/models".
                        It follows the format of RFC 3339, for example "2024-05-21T10:00:00Z".

                        This is used only when this rule contains "x-ai-eg-model" in its header matching
                        where the header value will be recognized as a "model" in "/models" endpoint.
                        All the matched models will share the same creation time.

                        Default to the creation timestamp of the AIGatewayRoute if not set.
                      format: date-time
                      type: string
                    modelsOwnedBy:
                      default: Envoy AI Gateway
                      description: |-
                        ModelsOwnedBy represents the owner of the running models serving by the backends,
                        which will be exported as the field of "OwnedBy" in openai-compatible API "/models".

                        This is used only when this rule contains "x-ai-eg-model" in its header matching
                        where the header value will be recognized as a "model" in "/models" endpoint.
                        All the matched models will share the same owner.

                        Default to "Envoy AI Gateway" if not set.
                      type: string
                    timeouts:
                      description: |-
                        Timeouts defines the timeouts that can be configured for an HTTP request.

                        If this field is not set, or the timeout.requestTimeout is nil, Envoy AI Gateway defaults to
                        set 60s for the request timeout as opposed to 15s of the Envoy Gateway's default value.

                        For streaming responses (like chat completions with stream=true), consider setting
                        longer timeouts as the response may take time until the completion.
                      properties:
                        backendRequest:
                          description: |-
                            BackendRequest specifies a timeout for an individual request from the gateway
                            to a backend. This covers the time from when the request first starts being
                            sent from the gateway to when the full response has been received from the backend.

                            Setting a timeout to the zero duration (e.g. "0s") SHOULD disable the timeout
                            completely. Implementations that cannot completely disable the timeout MUST
                            instead interpret the zero duration as the longest possible value to which
                            the timeout can be set.

                            An entire client HTTP transaction with a gateway, covered by the Request timeout,
                            may result in more than one call from the gateway to the destination backend,
                            for example, if automatic retries are supported.

                            The value of BackendRequest must be a Gateway API Duration string as defined by
                            GEP-2257.  When this field is unspecified, its behavior is implementation-specific;
                            when specified, the value of BackendRequest must be no more than the value of the
                            Request timeout (since the Request timeout encompasses the BackendRequest timeout).

                            Support: Extended
                          pattern: ^([0-9]{1,5}(h|m|s|ms)){1,4}$
                          type: string
                        request:
                          description: |-
                            Request specifies the maximum duration for a gateway to respond to an HTTP request.
                            If the gateway has not been able to respond before this deadline is met, the gateway
                            MUST return a timeout error.

                            For example, setting the `rules.timeouts.request` field to the value `10s` in an
                            `HTTPRoute` will cause a timeout if a client request is taking longer than 10 seconds
                            to complete.

                            Setting a timeout to the zero duration (e.g. "0s") SHOULD disable the timeout
                            completely. Implementations that cannot completely disable the timeout MUST
                            instead interpret the zero duration as the longest possible value to which
                            the timeout can be set.

                            This timeout is intended to cover as close to the whole request-response transaction
                            as possible although an implementation MAY choose to start the timeout after the entire
                            request stream has been received instead of immediately after the transaction is
                            initiated by the client.

                            The value of Request is a Gateway API Duration string as defined by GEP-2257. When this
                            field is unspecified, request timeout behavior is implementation-specific.

                            Support: Extended
                          pattern: ^([0-9]{1,5}(h|m|s|ms)){1,4}$
                          type: string
                      type: object
                      x-kubernetes-validations:
                      - message: backendRequest timeout cannot be longer than request
                          timeout
                        rule: '!(has(self.request) && has(self.backendRequest) &&
                          duration(self.request) != duration(''0s'') && duration(self.backendRequest)
                          > duration(self.request))'
                  type: object
                maxItems: 128
                type: array
              schema:
                description: |-
                  APISchema specifies the API schema of the input that the target Gateway(s) will receive.
                  Based on this schema, the ai-gateway will perform the necessary transformation to the
                  output schema specified in the selected AIServiceBackend during the routing process.

                  Currently, the only supported schema is OpenAI as the input schema.
                properties:
                  name:
                    description: Name is the name of the API schema of the AIGatewayRoute
                      or AIServiceBackend.
                    enum:
                    - OpenAI
                    - AWSBedrock
                    - AzureOpenAI
                    - GCPVertexAI
                    - GCPAnthropic
                    type: string
                  version:
                    description: |-
                      Version is the version of the API schema.

                      When the name is set to "OpenAI", this equals to the prefix of the OpenAI API endpoints. This defaults to "v1"
                      if not set or empty string. For example, "chat completions" API endpoint will be "/v1/chat/completions"
                      if the version is set to "v1".

                      This is especially useful when routing to the backend that has an OpenAI compatible API but has a different
                      versioning scheme. For example, Gemini OpenAI compatible API (https://ai.google.dev/gemini-api/docs/openai) uses
                      "/v1beta/openai" version prefix. Another example is that Cohere AI (https://docs.cohere.com/v2/docs/compatibility-api)
                      uses "/compatibility/v1" version prefix. On the other hand, DeepSeek (https://api-docs.deepseek.com/) doesn't
                      use version prefix, so the version can be set to an empty string.

                      When the name is set to AzureOpenAI, this version maps to "API Version" in the
                      Azure OpenAI API documentation (https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#rest-api-versioning).
                    type: string
                required:
                - name
                type: object
                x-kubernetes-validations:
                - rule: self.name == 'OpenAI'
              targetRefs:
                description: |-
                  TargetRefs are the names of the Gateway resources this AIGatewayRoute is being attached to.

                  Deprecated: use the ParentRefs field instead. This field will be dropped in Envoy AI Gateway v0.4.0.
                items:
                  description: |-
                    LocalPolicyTargetReferenceWithSectionName identifies an API object to apply a
                    direct policy to. This should be used as part of Policy resources that can
                    target single resources. For more information on how this policy attachment
                    mode works, and a sample Policy resource, refer to the policy attachment
                    documentation for Gateway API.

                    Note: This should only be used for direct policy attachment when references
                    to SectionName are actually needed. In all other cases,
                    LocalPolicyTargetReference should be used.
                  properties:
                    group:
                      description: Group is the group of the target resource.
                      maxLength: 253
                      pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                    kind:
                      description: Kind is kind of the target resource.
                      maxLength: 63
                      minLength: 1
                      pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                      type: string
                    name:
                      description: Name is the name of the target resource.
                      maxLength: 253
                      minLength: 1
                      type: string
                    sectionName:
                      description: |-
                        SectionName is the name of a section within the target resource. When
                        unspecified, this targetRef targets the entire resource. In the following
                        resources, SectionName is interpreted as the following:

                        * Gateway: Listener name
                        * HTTPRoute: HTTPRouteRule name
                        * Service: Port name

                        If a SectionName is specified, but does not exist on the targeted object,
                        the Policy must fail to attach, and the policy implementation should record
                        a `ResolvedRefs` or similar Condition in the Policy's status.
                      maxLength: 253
                      minLength: 1
                      pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                      type: string
                  required:
                  - group
                  - kind
                  - name
                  type: object
                maxItems: 128
                type: array
            required:
            - rules
            - schema
            type: object
            x-kubernetes-validations:
            - message: targetRefs is deprecated, use parentRefs only
              rule: '!has(self.parentRefs) || !has(self.targetRefs) || size(self.targetRefs)
                == 0'
          status:
            description: Status defines the status details of the AIGatewayRoute.
            properties:
              conditions:
                description: |-
                  Conditions is the list of conditions by the reconciliation result.
                  Currently, at most one condition is set.

                  Known .status.conditions.type are: "Accepted", "NotAccepted".
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
