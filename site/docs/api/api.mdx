---
id: api_references
title: API Reference
toc_min_heading_level: 2
toc_max_heading_level: 4
---


## aigateway.envoyproxy.io/v1alpha1

Package v1alpha1 contains API schema definitions for the aigateway.envoyproxy.io
API group.


## Resource Kinds

### Available Kinds
- [AIGatewayRoute](#aigatewayroute)
- [AIGatewayRouteList](#aigatewayroutelist)
- [AIServiceBackend](#aiservicebackend)
- [AIServiceBackendList](#aiservicebackendlist)
- [BackendSecurityPolicy](#backendsecuritypolicy)
- [BackendSecurityPolicyList](#backendsecuritypolicylist)

### Kind Definitions
#### AIGatewayRoute



**Appears in:**
- [AIGatewayRouteList](#aigatewayroutelist)

AIGatewayRoute combines multiple AIServiceBackends and attaching them to Gateway(s) resources.

This serves as a way to define a "unified" AI API for a Gateway which allows downstream
clients to use a single schema API to interact with multiple AI backends.

The schema field is used to determine the structure of the requests that the Gateway will
receive. And then the Gateway will route the traffic to the appropriate AIServiceBackend based
on the output schema of the AIServiceBackend while doing the other necessary jobs like
upstream authentication, rate limit, etc.

Envoy AI Gateway will generate the following k8s resources corresponding to the AIGatewayRoute:

  - HTTPRoute of the Gateway API as a top-level resource to bind all backends.
    The name of the HTTPRoute is the same as the AIGatewayRoute.
  - EnvoyExtensionPolicy of the Envoy Gateway API to attach the AI Gateway filter into the target Gateways.
    This will be created per Gateway, and its name is `ai-eg-eep-${gateway-name}`.
  - HTTPRouteFilter of the Envoy Gateway API per namespace for automatic hostname rewrite.
    The name of the HTTPRouteFilter is `ai-eg-host-rewrite`.

All of these resources are created in the same namespace as the AIGatewayRoute. Note that this is the implementation
detail subject to change. If you want to customize the default behavior of the Envoy AI Gateway, you can use these
resources as a reference and create your own resources. Alternatively, you can use EnvoyPatchPolicy API of the Envoy
Gateway to patch the generated resources. For example, you can configure the retry fallback behavior by attaching
BackendTrafficPolicy API of Envoy Gateway to the generated HTTPRoute.

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>AIGatewayRoute</code> resource"
/>

<ApiField
  name="metadata"
  type="[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#objectmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="spec"
  type="[AIGatewayRouteSpec](#aigatewayroutespec)"
  required="true"
  description="Spec defines the details of the AIGatewayRoute."
/><ApiField
  name="status"
  type="[AIGatewayRouteStatus](#aigatewayroutestatus)"
  required="true"
  description="Status defines the status details of the AIGatewayRoute."
/>


#### AIGatewayRouteList




AIGatewayRouteList contains a list of AIGatewayRoute.

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>AIGatewayRouteList</code> resource"
/>

<ApiField
  name="metadata"
  type="[ListMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#listmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="items"
  type="[AIGatewayRoute](#aigatewayroute) array"
  required="true"
  description=""
/>


#### AIServiceBackend



**Appears in:**
- [AIServiceBackendList](#aiservicebackendlist)

AIServiceBackend is a resource that represents a single backend for AIGatewayRoute.
A backend is a service that handles traffic with a concrete API specification.

A AIServiceBackend is "attached" to a Backend which is either a k8s Service or a Backend resource of the Envoy Gateway.

When a backend with an attached AIServiceBackend is used as a routing target in the AIGatewayRoute (more precisely, the
HTTPRouteSpec defined in the AIGatewayRoute), the ai-gateway will generate the necessary configuration to do
the backend specific logic in the final HTTPRoute.

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>AIServiceBackend</code> resource"
/>

<ApiField
  name="metadata"
  type="[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#objectmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="spec"
  type="[AIServiceBackendSpec](#aiservicebackendspec)"
  required="true"
  description="Spec defines the details of AIServiceBackend."
/><ApiField
  name="status"
  type="[AIServiceBackendStatus](#aiservicebackendstatus)"
  required="true"
  description="Status defines the status details of the AIServiceBackend."
/>


#### AIServiceBackendList




AIServiceBackendList contains a list of AIServiceBackends.

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>AIServiceBackendList</code> resource"
/>

<ApiField
  name="metadata"
  type="[ListMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#listmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="items"
  type="[AIServiceBackend](#aiservicebackend) array"
  required="true"
  description=""
/>


#### BackendSecurityPolicy



**Appears in:**
- [BackendSecurityPolicyList](#backendsecuritypolicylist)

BackendSecurityPolicy specifies configuration for authentication and authorization rules on the traffic
exiting the gateway to the backend.

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>BackendSecurityPolicy</code> resource"
/>

<ApiField
  name="metadata"
  type="[ObjectMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#objectmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="spec"
  type="[BackendSecurityPolicySpec](#backendsecuritypolicyspec)"
  required="true"
  description=""
/><ApiField
  name="status"
  type="[BackendSecurityPolicyStatus](#backendsecuritypolicystatus)"
  required="true"
  description="Status defines the status details of the BackendSecurityPolicy."
/>


#### BackendSecurityPolicyList




BackendSecurityPolicyList contains a list of BackendSecurityPolicy

##### Fields

<ApiField
  name="apiVersion"
  type="String"
  required="true"
  description="We are on version <code>aigateway.envoyproxy.io/v1alpha1</code> of the API."
/>

<ApiField
  name="kind"
  type="String"
  required="true"
  description="This is a <code>BackendSecurityPolicyList</code> resource"
/>

<ApiField
  name="metadata"
  type="[ListMeta](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#listmeta-v1-meta)"
  required="true"
  description="Refer to Kubernetes API documentation for fields of `metadata`."
/><ApiField
  name="items"
  type="[BackendSecurityPolicy](#backendsecuritypolicy) array"
  required="true"
  description=""
/>


## Supporting Types

### Available Types
- [AIGatewayFilterConfig](#aigatewayfilterconfig)
- [AIGatewayFilterConfigExternalProcessor](#aigatewayfilterconfigexternalprocessor)
- [AIGatewayFilterConfigType](#aigatewayfilterconfigtype)
- [AIGatewayRouteRule](#aigatewayrouterule)
- [AIGatewayRouteRuleBackendRef](#aigatewayrouterulebackendref)
- [AIGatewayRouteRuleMatch](#aigatewayrouterulematch)
- [AIGatewayRouteSpec](#aigatewayroutespec)
- [AIGatewayRouteStatus](#aigatewayroutestatus)
- [AIServiceBackendSpec](#aiservicebackendspec)
- [AIServiceBackendStatus](#aiservicebackendstatus)
- [APISchema](#apischema)
- [AWSCredentialsFile](#awscredentialsfile)
- [AWSOIDCExchangeToken](#awsoidcexchangetoken)
- [AzureOIDCExchangeToken](#azureoidcexchangetoken)
- [BackendSecurityPolicyAPIKey](#backendsecuritypolicyapikey)
- [BackendSecurityPolicyAWSCredentials](#backendsecuritypolicyawscredentials)
- [BackendSecurityPolicyAzureCredentials](#backendsecuritypolicyazurecredentials)
- [BackendSecurityPolicyGCPCredentials](#backendsecuritypolicygcpcredentials)
- [BackendSecurityPolicyOIDC](#backendsecuritypolicyoidc)
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)
- [BackendSecurityPolicyStatus](#backendsecuritypolicystatus)
- [BackendSecurityPolicyType](#backendsecuritypolicytype)
- [GCPServiceAccountImpersonationConfig](#gcpserviceaccountimpersonationconfig)
- [GCPWorkLoadIdentityFederationConfig](#gcpworkloadidentityfederationconfig)
- [GCPWorkloadIdentityProvider](#gcpworkloadidentityprovider)
- [LLMRequestCost](#llmrequestcost)
- [LLMRequestCostType](#llmrequestcosttype)
- [VersionedAPISchema](#versionedapischema)

### Type Definitions
#### AIGatewayFilterConfig



**Appears in:**
- [AIGatewayRouteSpec](#aigatewayroutespec)



##### Fields



<ApiField
  name="type"
  type="[AIGatewayFilterConfigType](#aigatewayfilterconfigtype)"
  required="true"
  defaultValue="ExternalProcessor"
  description="Type specifies the type of the filter configuration.<br />Currently, only ExternalProcessor is supported, and default is ExternalProcessor."
/><ApiField
  name="externalProcessor"
  type="[AIGatewayFilterConfigExternalProcessor](#aigatewayfilterconfigexternalprocessor)"
  required="false"
  description="ExternalProcessor is the configuration for the external processor filter.<br />This is optional, and if not set, the default values of Deployment spec will be used."
/>


#### AIGatewayFilterConfigExternalProcessor



**Appears in:**
- [AIGatewayFilterConfig](#aigatewayfilterconfig)



##### Fields



<ApiField
  name="resources"
  type="[ResourceRequirements](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#resourcerequirements-v1-core)"
  required="false"
  description="Resources required by the external processor container.<br />More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/<br />Note: when multiple AIGatewayRoute resources are attached to the same Gateway, and each<br />AIGatewayRoute has a different resource configuration, the ai-gateway will pick one of them<br />to configure the resource requirements of the external processor container."
/>


#### AIGatewayFilterConfigType

**Underlying type:** string

**Appears in:**
- [AIGatewayFilterConfig](#aigatewayfilterconfig)

AIGatewayFilterConfigType specifies the type of the filter configuration.



##### Possible Values

<ApiField
  name="ExternalProcessor"
  type="enum"
  required="false"
  description=""
/><ApiField
  name="DynamicModule"
  type="enum"
  required="false"
  description=""
/>
#### AIGatewayRouteRule



**Appears in:**
- [AIGatewayRouteSpec](#aigatewayroutespec)

AIGatewayRouteRule is a rule that defines the routing behavior of the AIGatewayRoute.

##### Fields



<ApiField
  name="backendRefs"
  type="[AIGatewayRouteRuleBackendRef](#aigatewayrouterulebackendref) array"
  required="false"
  description="BackendRefs is the list of AIServiceBackend that this rule will route the traffic to.<br />Each backend can have a weight that determines the traffic distribution.<br />The namespace of each backend is `local`, i.e. the same namespace as the AIGatewayRoute.<br />By configuring multiple backends, you can achieve the fallback behavior in the case of<br />the primary backend is not available combined with the BackendTrafficPolicy of Envoy Gateway.<br />Please refer to https://gateway.envoyproxy.io/docs/tasks/traffic/failover/ as well as<br />https://gateway.envoyproxy.io/docs/tasks/traffic/retry/."
/><ApiField
  name="matches"
  type="[AIGatewayRouteRuleMatch](#aigatewayrouterulematch) array"
  required="false"
  description="Matches is the list of AIGatewayRouteMatch that this rule will match the traffic to.<br />This is a subset of the HTTPRouteMatch in the Gateway API. See for the details:<br />https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPRouteMatch"
/><ApiField
  name="timeouts"
  type="[HTTPRouteTimeouts](https://gateway-api.sigs.k8s.io/reference/spec/?h=httproutetimeouts#httproutetimeouts)"
  required="false"
  description="Timeouts defines the timeouts that can be configured for an HTTP request.<br />If this field is not set, or the timeout.requestTimeout is nil, Envoy AI Gateway defaults to<br />set 60s for the request timeout as opposed to 15s of the Envoy Gateway's default value.<br />For streaming responses (like chat completions with stream=true), consider setting<br />longer timeouts as the response may take time until the completion."
/><ApiField
  name="modelsOwnedBy"
  type="string"
  required="false"
  defaultValue="Envoy AI Gateway"
  description="ModelsOwnedBy represents the owner of the running models serving by the backends,<br />which will be exported as the field of `OwnedBy` in openai-compatible API `/models`.<br />This is used only when this rule contains `x-ai-eg-model` in its header matching<br />where the header value will be recognized as a `model` in `/models` endpoint.<br />All the matched models will share the same owner.<br />Default to `Envoy AI Gateway` if not set."
/><ApiField
  name="modelsCreatedAt"
  type="[Time](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#time-v1-meta)"
  required="false"
  description="ModelsCreatedAt represents the creation timestamp of the running models serving by the backends,<br />which will be exported as the field of `Created` in openai-compatible API `/models`.<br />It follows the format of RFC 3339, for example `2024-05-21T10:00:00Z`.<br />This is used only when this rule contains `x-ai-eg-model` in its header matching<br />where the header value will be recognized as a `model` in `/models` endpoint.<br />All the matched models will share the same creation time.<br />Default to the creation timestamp of the AIGatewayRoute if not set."
/>


#### AIGatewayRouteRuleBackendRef



**Appears in:**
- [AIGatewayRouteRule](#aigatewayrouterule)

AIGatewayRouteRuleBackendRef is a reference to a backend with a weight.

##### Fields



<ApiField
  name="name"
  type="string"
  required="true"
  description="Name is the name of the AIServiceBackend."
/><ApiField
  name="modelNameOverride"
  type="string"
  required="true"
  description="Name of the model in the backend. If provided this will override the name provided in the request."
/><ApiField
  name="weight"
  type="integer"
  required="false"
  defaultValue="1"
  description="Weight is the weight of the AIServiceBackend. This is exactly the same as the weight in<br />the BackendRef in the Gateway API. See for the details:<br />https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.BackendRef<br />Default is 1."
/><ApiField
  name="priority"
  type="integer"
  required="false"
  defaultValue="0"
  description="Priority is the priority of the AIServiceBackend. This sets the priority on the underlying endpoints.<br />See: https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/load_balancing/priority<br />Note: This will override the `faillback` property of the underlying Envoy Gateway Backend<br />Default is 0."
/>


#### AIGatewayRouteRuleMatch



**Appears in:**
- [AIGatewayRouteRule](#aigatewayrouterule)



##### Fields



<ApiField
  name="headers"
  type="HTTPHeaderMatch array"
  required="false"
  description="Headers specifies HTTP request header matchers. See HeaderMatch in the Gateway API for the details:<br />https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPHeaderMatch"
/>


#### AIGatewayRouteSpec



**Appears in:**
- [AIGatewayRoute](#aigatewayroute)

AIGatewayRouteSpec details the AIGatewayRoute configuration.

##### Fields



<ApiField
  name="targetRefs"
  type="[LocalPolicyTargetReferenceWithSectionName](https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io/v1alpha2.LocalPolicyTargetReferenceWithSectionName) array"
  required="true"
  description="TargetRefs are the names of the Gateway resources this AIGatewayRoute is being attached to.<br />Deprecated: use the ParentRefs field instead. This field will be dropped in Envoy AI Gateway v0.4.0."
/><ApiField
  name="parentRefs"
  type="ParentReference array"
  required="true"
  description="ParentRefs are the names of the Gateway resources this AIGatewayRoute is being attached to.<br />Cross namespace references are not supported. In other words, the Gateway resources must be in the<br />same namespace as the AIGatewayRoute. Currently, each reference's Kind must be Gateway."
/><ApiField
  name="schema"
  type="[VersionedAPISchema](#versionedapischema)"
  required="true"
  description="APISchema specifies the API schema of the input that the target Gateway(s) will receive.<br />Based on this schema, the ai-gateway will perform the necessary transformation to the<br />output schema specified in the selected AIServiceBackend during the routing process.<br />Currently, the only supported schema is OpenAI as the input schema."
/><ApiField
  name="rules"
  type="[AIGatewayRouteRule](#aigatewayrouterule) array"
  required="true"
  description="Rules is the list of AIGatewayRouteRule that this AIGatewayRoute will match the traffic to.<br />Each rule is a subset of the HTTPRoute in the Gateway API (https://gateway-api.sigs.k8s.io/api-types/httproute/).<br />AI Gateway controller will generate a HTTPRoute based on the configuration given here with the additional<br />modifications to achieve the necessary jobs, notably inserting the AI Gateway filter responsible for<br />the transformation of the request and response, etc.<br />In the matching conditions in the AIGatewayRouteRule, `x-ai-eg-model` header is available<br />if we want to describe the routing behavior based on the model name. The model name is extracted<br />from the request content before the routing decision.<br />How multiple rules are matched is the same as the Gateway API. See for the details:<br />https://gateway-api.sigs.k8s.io/reference/spec/#gateway.networking.k8s.io%2fv1.HTTPRoute"
/><ApiField
  name="filterConfig"
  type="[AIGatewayFilterConfig](#aigatewayfilterconfig)"
  required="true"
  description="FilterConfig is the configuration for the AI Gateway filter inserted in the generated HTTPRoute.<br />An AI Gateway filter is responsible for the transformation of the request and response<br />as well as the routing behavior based on the model name extracted from the request content, etc.<br />Currently, the filter is only implemented as an external processor filter, which might be<br />extended to other types of filters in the future. See https://github.com/envoyproxy/ai-gateway/issues/90"
/><ApiField
  name="llmRequestCosts"
  type="[LLMRequestCost](#llmrequestcost) array"
  required="false"
  description="LLMRequestCosts specifies how to capture the cost of the LLM-related request, notably the token usage.<br />The AI Gateway filter will capture each specified number and store it in the Envoy's dynamic<br />metadata per HTTP request. The namespaced key is `io.envoy.ai_gateway`,<br />For example, let's say we have the following LLMRequestCosts configuration:<br />```yaml<br />	llmRequestCosts:<br />	- metadataKey: llm_input_token<br />	  type: InputToken<br />	- metadataKey: llm_output_token<br />	  type: OutputToken<br />	- metadataKey: llm_total_token<br />	  type: TotalToken<br />```<br />Then, with the following BackendTrafficPolicy of Envoy Gateway, you can have three<br />rate limit buckets for each unique x-user-id header value. One bucket is for the input token,<br />the other is for the output token, and the last one is for the total token.<br />Each bucket will be reduced by the corresponding token usage captured by the AI Gateway filter.<br />```yaml<br />	apiVersion: gateway.envoyproxy.io/v1alpha1<br />	kind: BackendTrafficPolicy<br />	metadata:<br />	  name: some-example-token-rate-limit<br />	  namespace: default<br />	spec:<br />	  targetRefs:<br />	  - group: gateway.networking.k8s.io<br />	     kind: HTTPRoute<br />	     name: usage-rate-limit<br />	  rateLimit:<br />	    type: Global<br />	    global:<br />	      rules:<br />	        - clientSelectors:<br />	            # Do the rate limiting based on the x-user-id header.<br />	            - headers:<br />	                - name: x-user-id<br />	                  type: Distinct<br />	          limit:<br />	            # Configures the number of `tokens` allowed per hour.<br />	            requests: 10000<br />	            unit: Hour<br />	          cost:<br />	            request:<br />	              from: Number<br />	              # Setting the request cost to zero allows to only check the rate limit budget,<br />	              # and not consume the budget on the request path.<br />	              number: 0<br />	            # This specifies the cost of the response retrieved from the dynamic metadata set by the AI Gateway filter.<br />	            # The extracted value will be used to consume the rate limit budget, and subsequent requests will be rate limited<br />	            # if the budget is exhausted.<br />	            response:<br />	              from: Metadata<br />	              metadata:<br />	                namespace: io.envoy.ai_gateway<br />	                key: llm_input_token<br />	        - clientSelectors:<br />	            - headers:<br />	                - name: x-user-id<br />	                  type: Distinct<br />	          limit:<br />	            requests: 10000<br />	            unit: Hour<br />	          cost:<br />	            request:<br />	              from: Number<br />	              number: 0<br />	            response:<br />	              from: Metadata<br />	              metadata:<br />	                namespace: io.envoy.ai_gateway<br />	                key: llm_output_token<br />	        - clientSelectors:<br />	            - headers:<br />	                - name: x-user-id<br />	                  type: Distinct<br />	          limit:<br />	            requests: 10000<br />	            unit: Hour<br />	          cost:<br />	            request:<br />	              from: Number<br />	              number: 0<br />	            response:<br />	              from: Metadata<br />	              metadata:<br />	                namespace: io.envoy.ai_gateway<br />	                key: llm_total_token<br />```<br />Note that when multiple AIGatewayRoute resources are attached to the same Gateway, and<br />different costs are configured for the same metadata key, the ai-gateway will pick one of them<br />to configure the metadata key in the generated HTTPRoute, and ignore the rest."
/>


#### AIGatewayRouteStatus



**Appears in:**
- [AIGatewayRoute](#aigatewayroute)

AIGatewayRouteStatus contains the conditions by the reconciliation result.

##### Fields



<ApiField
  name="conditions"
  type="[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#condition-v1-meta) array"
  required="true"
  description="Conditions is the list of conditions by the reconciliation result.<br />Currently, at most one condition is set.<br />Known .status.conditions.type are: `Accepted`, `NotAccepted`."
/>


#### AIServiceBackendSpec



**Appears in:**
- [AIServiceBackend](#aiservicebackend)

AIServiceBackendSpec details the AIServiceBackend configuration.

##### Fields



<ApiField
  name="schema"
  type="[VersionedAPISchema](#versionedapischema)"
  required="true"
  description="APISchema specifies the API schema of the output format of requests from<br />Envoy that this AIServiceBackend can accept as incoming requests.<br />Based on this schema, the ai-gateway will perform the necessary transformation for<br />the pair of AIGatewayRouteSpec.APISchema and AIServiceBackendSpec.APISchema.<br />This is required to be set."
/><ApiField
  name="backendRef"
  type="[BackendObjectReference](https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.BackendObjectReference)"
  required="true"
  description="BackendRef is the reference to the Backend resource that this AIServiceBackend corresponds to.<br />A backend must be a Backend resource of Envoy Gateway. Note that k8s Service will be supported<br />as a backend in the future.<br />This is required to be set."
/><ApiField
  name="backendSecurityPolicyRef"
  type="[LocalObjectReference](https://gateway-api.sigs.k8s.io/reference/spec/?h=httproutetimeouts#localobjectreference)"
  required="false"
  description="BackendSecurityPolicyRef is the name of the BackendSecurityPolicy resources this backend<br />is being attached to."
/>


#### AIServiceBackendStatus



**Appears in:**
- [AIServiceBackend](#aiservicebackend)

AIServiceBackendStatus contains the conditions by the reconciliation result.

##### Fields



<ApiField
  name="conditions"
  type="[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#condition-v1-meta) array"
  required="true"
  description="Conditions is the list of conditions by the reconciliation result.<br />Currently, at most one condition is set.<br />Known .status.conditions.type are: `Accepted`, `NotAccepted`."
/>


#### APISchema

**Underlying type:** string

**Appears in:**
- [VersionedAPISchema](#versionedapischema)

APISchema defines the API schema.



##### Possible Values

<ApiField
  name="OpenAI"
  type="enum"
  required="false"
  description="APISchemaOpenAI is the OpenAI schema.<br />https://github.com/openai/openai-openapi<br />"
/><ApiField
  name="AWSBedrock"
  type="enum"
  required="false"
  description="APISchemaAWSBedrock is the AWS Bedrock schema.<br />https://docs.aws.amazon.com/bedrock/latest/APIReference/API_Operations_Amazon_Bedrock_Runtime.html<br />"
/><ApiField
  name="AzureOpenAI"
  type="enum"
  required="false"
  description="APISchemaAzureOpenAI APISchemaAzure is the Azure OpenAI schema.<br />https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#api-specs<br />"
/><ApiField
  name="GCPVertexAI"
  type="enum"
  required="false"
  description="APISchemaGCPVertexAI is the schema followed by Gemini models hosted on GCP's Vertex AI platform.<br />Note: Using this schema requires a BackendSecurityPolicy to be configured and attached,<br />as the transformation will use the gcp-region and project-name from the BackendSecurityPolicy.<br />https://cloud.google.com/vertex-ai/docs/reference/rest/v1/projects.locations.endpoints/generateContent?hl=en<br />"
/><ApiField
  name="GCPAnthropic"
  type="enum"
  required="false"
  description="APISchemaGCPAnthropic is the schema followed by Anthropic models hosted on GCP's Vertex AI platform.<br />This is majorly the Anthropic API with some GCP specific parameters as described in below URL.<br />https://docs.anthropic.com/en/api/claude-on-vertex-ai<br />"
/>
#### AWSCredentialsFile



**Appears in:**
- [BackendSecurityPolicyAWSCredentials](#backendsecuritypolicyawscredentials)

AWSCredentialsFile specifies the credentials file to use for the AWS provider.
Envoy reads the secret file, and the profile to use is specified by the Profile field.

##### Fields



<ApiField
  name="secretRef"
  type="[SecretObjectReference](https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.SecretObjectReference)"
  required="true"
  description="SecretRef is the reference to the credential file.<br />The secret should contain the AWS credentials file keyed on `credentials`."
/><ApiField
  name="profile"
  type="string"
  required="true"
  defaultValue="default"
  description="Profile is the profile to use in the credentials file."
/>


#### AWSOIDCExchangeToken



**Appears in:**
- [BackendSecurityPolicyAWSCredentials](#backendsecuritypolicyawscredentials)

AWSOIDCExchangeToken specifies credentials to obtain oidc token from a sso server.
For AWS, the controller will query STS to obtain AWS AccessKeyId, SecretAccessKey, and SessionToken,
and store them in a temporary credentials file.

##### Fields



<ApiField
  name="oidc"
  type="[OIDC](https://gateway.envoyproxy.io/docs/api/extension_types/#oidc)"
  required="true"
  description="OIDC is used to obtain oidc tokens via an SSO server which will be used to exchange for provider credentials."
/><ApiField
  name="grantType"
  type="string"
  required="false"
  description="GrantType is the method application gets access token."
/><ApiField
  name="aud"
  type="string"
  required="false"
  description="Aud defines the audience that this ID Token is intended for."
/><ApiField
  name="awsRoleArn"
  type="string"
  required="true"
  description="AwsRoleArn is the AWS IAM Role with the permission to use specific resources in AWS account<br />which maps to the temporary AWS security credentials exchanged using the authentication token issued by OIDC provider."
/>


#### AzureOIDCExchangeToken



**Appears in:**
- [BackendSecurityPolicyAzureCredentials](#backendsecuritypolicyazurecredentials)

AzureOIDCExchangeToken specifies credentials to obtain oidc token from a sso server.
For Azure, the controller will query Azure Entra ID to get an Azure Access Token,
and store them in a secret.

##### Fields



<ApiField
  name="oidc"
  type="[OIDC](https://gateway.envoyproxy.io/docs/api/extension_types/#oidc)"
  required="true"
  description="OIDC is used to obtain oidc tokens via an SSO server which will be used to exchange for provider credentials."
/><ApiField
  name="grantType"
  type="string"
  required="false"
  description="GrantType is the method application gets access token."
/><ApiField
  name="aud"
  type="string"
  required="false"
  description="Aud defines the audience that this ID Token is intended for."
/>


#### BackendSecurityPolicyAPIKey



**Appears in:**
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)

BackendSecurityPolicyAPIKey specifies the API key.

##### Fields



<ApiField
  name="secretRef"
  type="[SecretObjectReference](https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.SecretObjectReference)"
  required="true"
  description="SecretRef is the reference to the secret containing the API key.<br />ai-gateway must be given the permission to read this secret.<br />The key of the secret should be `apiKey`."
/>


#### BackendSecurityPolicyAWSCredentials



**Appears in:**
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)

BackendSecurityPolicyAWSCredentials contains the supported authentication mechanisms to access aws.

##### Fields



<ApiField
  name="region"
  type="string"
  required="true"
  description="Region specifies the AWS region associated with the policy."
/><ApiField
  name="credentialsFile"
  type="[AWSCredentialsFile](#awscredentialsfile)"
  required="false"
  description="CredentialsFile specifies the credentials file to use for the AWS provider."
/><ApiField
  name="oidcExchangeToken"
  type="[AWSOIDCExchangeToken](#awsoidcexchangetoken)"
  required="false"
  description="OIDCExchangeToken specifies the oidc configurations used to obtain an oidc token. The oidc token will be<br />used to obtain temporary credentials to access AWS."
/>


#### BackendSecurityPolicyAzureCredentials



**Appears in:**
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)

BackendSecurityPolicyAzureCredentials contains the supported authentication mechanisms to access Azure.
Only one of ClientSecretRef or OIDCExchangeToken must be specified. Credentials will not be generated if
neither are set.

##### Fields



<ApiField
  name="clientID"
  type="string"
  required="true"
  description="ClientID is a unique identifier for an application in Azure."
/><ApiField
  name="tenantID"
  type="string"
  required="true"
  description="TenantId is a unique identifier for an Azure Active Directory instance."
/><ApiField
  name="clientSecretRef"
  type="[SecretObjectReference](https://gateway-api.sigs.k8s.io/references/spec/#gateway.networking.k8s.io/v1.SecretObjectReference)"
  required="false"
  description="ClientSecretRef is the reference to the secret containing the Azure client secret.<br />ai-gateway must be given the permission to read this secret.<br />The key of secret should be `client-secret`."
/><ApiField
  name="oidcExchangeToken"
  type="[AzureOIDCExchangeToken](#azureoidcexchangetoken)"
  required="false"
  description="OIDCExchangeToken specifies the oidc configurations used to obtain an oidc token. The oidc token will be<br />used to obtain temporary credentials to access Azure."
/>


#### BackendSecurityPolicyGCPCredentials



**Appears in:**
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)

BackendSecurityPolicyGCPCredentials contains the supported authentication mechanisms to access GCP.

##### Fields



<ApiField
  name="projectName"
  type="string"
  required="true"
  description="ProjectName is the GCP project name."
/><ApiField
  name="region"
  type="string"
  required="true"
  description="Region is the GCP region associated with the policy."
/><ApiField
  name="workLoadIdentityFederationConfig"
  type="[GCPWorkLoadIdentityFederationConfig](#gcpworkloadidentityfederationconfig)"
  required="true"
  description="WorkLoadIdentityFederationConfig is the configuration for the GCP Workload Identity Federation."
/>


#### BackendSecurityPolicyOIDC



**Appears in:**
- [AWSOIDCExchangeToken](#awsoidcexchangetoken)
- [AzureOIDCExchangeToken](#azureoidcexchangetoken)
- [GCPWorkloadIdentityProvider](#gcpworkloadidentityprovider)

BackendSecurityPolicyOIDC specifies OIDC related fields.

##### Fields



<ApiField
  name="oidc"
  type="[OIDC](https://gateway.envoyproxy.io/docs/api/extension_types/#oidc)"
  required="true"
  description="OIDC is used to obtain oidc tokens via an SSO server which will be used to exchange for provider credentials."
/><ApiField
  name="grantType"
  type="string"
  required="false"
  description="GrantType is the method application gets access token."
/><ApiField
  name="aud"
  type="string"
  required="false"
  description="Aud defines the audience that this ID Token is intended for."
/>


#### BackendSecurityPolicySpec



**Appears in:**
- [BackendSecurityPolicy](#backendsecuritypolicy)

BackendSecurityPolicySpec specifies authentication rules on access the provider from the Gateway.
Only one mechanism to access a backend(s) can be specified.

Only one type of BackendSecurityPolicy can be defined.

##### Fields



<ApiField
  name="type"
  type="[BackendSecurityPolicyType](#backendsecuritypolicytype)"
  required="true"
  description="Type specifies the type of the backend security policy."
/><ApiField
  name="apiKey"
  type="[BackendSecurityPolicyAPIKey](#backendsecuritypolicyapikey)"
  required="false"
  description="APIKey is a mechanism to access a backend(s). The API key will be injected into the Authorization header."
/><ApiField
  name="awsCredentials"
  type="[BackendSecurityPolicyAWSCredentials](#backendsecuritypolicyawscredentials)"
  required="false"
  description="AWSCredentials is a mechanism to access a backend(s). AWS specific logic will be applied."
/><ApiField
  name="azureCredentials"
  type="[BackendSecurityPolicyAzureCredentials](#backendsecuritypolicyazurecredentials)"
  required="false"
  description="AzureCredentials is a mechanism to access a backend(s). Azure OpenAI specific logic will be applied."
/><ApiField
  name="gcpCredentials"
  type="[BackendSecurityPolicyGCPCredentials](#backendsecuritypolicygcpcredentials)"
  required="false"
  description="GCPCredentials is a mechanism to access a backend(s). GCP specific logic will be applied."
/>


#### BackendSecurityPolicyStatus



**Appears in:**
- [BackendSecurityPolicy](#backendsecuritypolicy)

BackendSecurityPolicyStatus contains the conditions by the reconciliation result.

##### Fields



<ApiField
  name="conditions"
  type="[Condition](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.29/#condition-v1-meta) array"
  required="true"
  description="Conditions is the list of conditions by the reconciliation result.<br />Currently, at most one condition is set.<br />Known .status.conditions.type are: `Accepted`, `NotAccepted`."
/>


#### BackendSecurityPolicyType

**Underlying type:** string

**Appears in:**
- [BackendSecurityPolicySpec](#backendsecuritypolicyspec)

BackendSecurityPolicyType specifies the type of auth mechanism used to access a backend.



##### Possible Values

<ApiField
  name="APIKey"
  type="enum"
  required="false"
  description=""
/><ApiField
  name="AWSCredentials"
  type="enum"
  required="false"
  description=""
/><ApiField
  name="AzureCredentials"
  type="enum"
  required="false"
  description=""
/><ApiField
  name="GCPCredentials"
  type="enum"
  required="false"
  description=""
/>
#### GCPServiceAccountImpersonationConfig



**Appears in:**
- [GCPWorkLoadIdentityFederationConfig](#gcpworkloadidentityfederationconfig)



##### Fields



<ApiField
  name="serviceAccountName"
  type="string"
  required="true"
  description="ServiceAccountName is the name of the service account to impersonate."
/><ApiField
  name="serviceAccountProjectName"
  type="string"
  required="true"
  description="ServiceAccountProjectName is the project name in which the service account is registered."
/>


#### GCPWorkLoadIdentityFederationConfig



**Appears in:**
- [BackendSecurityPolicyGCPCredentials](#backendsecuritypolicygcpcredentials)



##### Fields



<ApiField
  name="projectID"
  type="string"
  required="true"
  description="ProjectID is the GCP project ID."
/><ApiField
  name="workloadIdentityProvider"
  type="[GCPWorkloadIdentityProvider](#gcpworkloadidentityprovider)"
  required="true"
  description="WorkloadIdentityProvider is the external auth provider to be used to authenticate against GCP.<br />https://cloud.google.com/iam/docs/workload-identity-federation?hl=en<br />Currently only OIDC is supported."
/><ApiField
  name="workloadIdentityPoolName"
  type="string"
  required="true"
  description="WorkloadIdentityPoolName is the name of the workload identity pool defined in GCP.<br />https://cloud.google.com/iam/docs/workload-identity-federation?hl=en"
/><ApiField
  name="serviceAccountImpersonation"
  type="[GCPServiceAccountImpersonationConfig](#gcpserviceaccountimpersonationconfig)"
  required="false"
  description="ServiceAccountImpersonation is the service account impersonation configuration.<br />This is used to impersonate a service account when getting access token."
/>


#### GCPWorkloadIdentityProvider



**Appears in:**
- [GCPWorkLoadIdentityFederationConfig](#gcpworkloadidentityfederationconfig)

GCPWorkloadIdentityProvider specifies the external identity provider to be used to authenticate against GCP.
The external identity provider can be AWS, Microsoft, etc but must be pre-registered in the GCP project

https://cloud.google.com/iam/docs/workload-identity-federation

##### Fields



<ApiField
  name="name"
  type="string"
  required="true"
  description="Name of the external identity provider as registered on Google Cloud Platform."
/><ApiField
  name="OIDCProvider"
  type="[BackendSecurityPolicyOIDC](#backendsecuritypolicyoidc)"
  required="true"
  description="OIDCProvider is the generic OIDCProvider fields."
/>


#### LLMRequestCost



**Appears in:**
- [AIGatewayRouteSpec](#aigatewayroutespec)

LLMRequestCost configures each request cost.

##### Fields



<ApiField
  name="metadataKey"
  type="string"
  required="true"
  description="MetadataKey is the key of the metadata to store this cost of the request."
/><ApiField
  name="type"
  type="[LLMRequestCostType](#llmrequestcosttype)"
  required="true"
  description="Type specifies the type of the request cost. The default is `OutputToken`,<br />and it uses `output token` as the cost. The other types are `InputToken`, `TotalToken`,<br />and `CEL`."
/><ApiField
  name="cel"
  type="string"
  required="false"
  description="CEL is the CEL expression to calculate the cost of the request.<br />The CEL expression must return a signed or unsigned integer. If the<br />return value is negative, it will be error.<br />The expression can use the following variables:<br />	* model: the model name extracted from the request content. Type: string.<br />	* backend: the backend name in the form of `name.namespace`. Type: string.<br />	* input_tokens: the number of input tokens. Type: unsigned integer.<br />	* output_tokens: the number of output tokens. Type: unsigned integer.<br />	* total_tokens: the total number of tokens. Type: unsigned integer.<br />For example, the following expressions are valid:<br />	* `model == 'llama' ?  input_tokens + output_token * 0.5 : total_tokens`<br />	* `backend == 'foo.default' ?  input_tokens + output_tokens : total_tokens`<br />	* `input_tokens + output_tokens + total_tokens`<br />	* `input_tokens * output_tokens`"
/>


#### LLMRequestCostType

**Underlying type:** string

**Appears in:**
- [LLMRequestCost](#llmrequestcost)

LLMRequestCostType specifies the type of the LLMRequestCost.



##### Possible Values

<ApiField
  name="InputToken"
  type="enum"
  required="false"
  description="LLMRequestCostTypeInputToken is the cost type of the input token.<br />"
/><ApiField
  name="OutputToken"
  type="enum"
  required="false"
  description="LLMRequestCostTypeOutputToken is the cost type of the output token.<br />"
/><ApiField
  name="TotalToken"
  type="enum"
  required="false"
  description="LLMRequestCostTypeTotalToken is the cost type of the total token.<br />"
/><ApiField
  name="CEL"
  type="enum"
  required="false"
  description="LLMRequestCostTypeCEL is for calculating the cost using the CEL expression.<br />"
/>
#### VersionedAPISchema



**Appears in:**
- [AIGatewayRouteSpec](#aigatewayroutespec)
- [AIServiceBackendSpec](#aiservicebackendspec)

VersionedAPISchema defines the API schema of either AIGatewayRoute (the input) or AIServiceBackend (the output).

This allows the ai-gateway to understand the input and perform the necessary transformation
depending on the API schema pair (input, output).

Note that this is vendor specific, and the stability of the API schema is not guaranteed by
the ai-gateway, but by the vendor via proper versioning.

##### Fields



<ApiField
  name="name"
  type="[APISchema](#apischema)"
  required="true"
  description="Name is the name of the API schema of the AIGatewayRoute or AIServiceBackend."
/><ApiField
  name="version"
  type="string"
  required="true"
  description="Version is the version of the API schema.<br />When the name is set to `OpenAI`, this equals to the prefix of the OpenAI API endpoints. This defaults to `v1`<br />if not set or empty string. For example, `chat completions` API endpoint will be `/v1/chat/completions`<br />if the version is set to `v1`.<br />This is especially useful when routing to the backend that has an OpenAI compatible API but has a different<br />versioning scheme. For example, Gemini OpenAI compatible API (https://ai.google.dev/gemini-api/docs/openai) uses<br />`/v1beta/openai` version prefix. Another example is that Cohere AI (https://docs.cohere.com/v2/docs/compatibility-api)<br />uses `/compatibility/v1` version prefix. On the other hand, DeepSeek (https://api-docs.deepseek.com/) doesn't<br />use version prefix, so the version can be set to an empty string.<br />When the name is set to AzureOpenAI, this version maps to `API Version` in the<br />Azure OpenAI API documentation (https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#rest-api-versioning)."
/>


