// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Copyright Envoy AI Gateway Authors.
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at.
// the root of the repo.

package main

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os/exec"
	"strings"

	"github.com/fatih/color"
	"k8s.io/client-go/kubernetes"
)

// uninstall implements subCmd[cmdUninstall]. This function orchestrates the uninstallation
// of Envoy AI Gateway and optionally Envoy Gateway.
func uninstall(ctx context.Context, cmd cmdUninstall, stdout, stderr io.Writer) error {
	stderrLogger := slog.New(slog.NewTextHandler(stderr, &slog.HandlerOptions{}))
	if !cmd.Debug {
		stderrLogger = slog.New(slog.NewTextHandler(io.Discard, &slog.HandlerOptions{}))
	}

	// Create Kubernetes client.
	k8sClient, err := createKubernetesClient()
	if err != nil {
		return fmt.Errorf("failed to create Kubernetes client: %w", err)
	}

	// Create uninstallation orchestrator.
	uninstaller := &uninstallOrchestrator{
		cmd:       cmd,
		stdout:    stdout,
		stderr:    stderr,
		logger:    stderrLogger,
		k8sClient: k8sClient,
	}

	// Run the uninstallation process.
	return uninstaller.run(ctx)
}

// uninstallOrchestrator manages the uninstallation process.
type uninstallOrchestrator struct {
	cmd       cmdUninstall
	stdout    io.Writer
	stderr    io.Writer
	logger    *slog.Logger
	k8sClient kubernetes.Interface
}

// run executes the uninstallation process.
func (u *uninstallOrchestrator) run(ctx context.Context) error {
	if u.cmd.DryRun {
		u.printWarning("Dry run mode - no changes will be made")
	}

	// Print warning banner.
	u.printUninstallBanner()

	// Ask for confirmation unless skipped.
	if !u.cmd.SkipConfirmation && !u.cmd.DryRun {
		if !u.askConfirmation() {
			u.printInfo("Uninstallation cancelled by user")
			return nil
		}
	}

	// Step 1: Check what's installed.
	installedComponents, err := u.checkInstalledComponents(ctx)
	if err != nil {
		return fmt.Errorf("failed to check installed components: %w", err)
	}

	if len(installedComponents) == 0 {
		u.printInfo("No AI Gateway components found to uninstall")
		u.printCompletionBanner()
		return nil
	}

	// Step 2: Remove AI Gateway resources.
	if err := u.removeAIGatewayResources(ctx, installedComponents); err != nil {
		if !u.cmd.Force {
			return fmt.Errorf("failed to remove AI Gateway resources: %w", err)
		}
		u.printWarning(fmt.Sprintf("Failed to remove some AI Gateway resources: %v", err))
	}

	// Step 3: Remove Envoy Gateway configuration.
	if err := u.removeEnvoyGatewayConfig(ctx); err != nil {
		if !u.cmd.Force {
			return fmt.Errorf("failed to remove Envoy Gateway configuration: %w", err)
		}
		u.printWarning(fmt.Sprintf("Failed to remove Envoy Gateway configuration: %v", err))
	}

	// Step 4: Remove Envoy Gateway (if requested).
	if !u.cmd.KeepEnvoyGateway {
		if err := u.removeEnvoyGateway(ctx); err != nil {
			if !u.cmd.Force {
				return fmt.Errorf("failed to remove Envoy Gateway: %w", err)
			}
			u.printWarning(fmt.Sprintf("Failed to remove Envoy Gateway: %v", err))
		}
	}

	// Step 5: Verify cleanup.
	if err := u.verifyCleanup(ctx); err != nil {
		u.printWarning(fmt.Sprintf("Cleanup verification failed: %v", err))
	}

	u.printCompletionBanner()
	return nil
}

// printUninstallBanner prints a warning banner.
func (u *uninstallOrchestrator) printUninstallBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               🗑️  Envoy AI Gateway Uninstaller               ║
║                                                              ║
║   ⚠️  WARNING: This will remove AI Gateway and optionally    ║
║   Envoy Gateway from your cluster. This action cannot be    ║
║   undone easily.                                             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`
	fmt.Fprint(u.stdout, color.YellowString(banner))
}

// printCompletionBanner prints a completion banner.
func (u *uninstallOrchestrator) printCompletionBanner() {
	banner := `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║               ✅ Uninstallation Complete!                    ║
║                                                              ║
║   Envoy AI Gateway has been successfully removed from       ║
║   your cluster.                                              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`
	fmt.Fprint(u.stdout, color.GreenString(banner))
}

// printStep prints a step with an icon and message.
func (u *uninstallOrchestrator) printStep(icon, message string) {
	fmt.Fprintf(u.stdout, "%s %s\n", icon, color.CyanString(message))
}

// printSuccess prints a success message.
func (u *uninstallOrchestrator) printSuccess(message string) {
	fmt.Fprintf(u.stdout, "✅ %s\n", color.GreenString(message))
}

// printError prints an error message.
func (u *uninstallOrchestrator) printError(message string) {
	fmt.Fprintf(u.stdout, "❌ %s\n", color.RedString(message))
}

// printWarning prints a warning message.
func (u *uninstallOrchestrator) printWarning(message string) {
	fmt.Fprintf(u.stdout, "⚠️  %s\n", color.YellowString(message))
}

// printInfo prints an info message.
func (u *uninstallOrchestrator) printInfo(message string) {
	fmt.Fprintf(u.stdout, "ℹ️  %s\n", color.BlueString(message))
}

// askConfirmation asks the user for confirmation.
func (u *uninstallOrchestrator) askConfirmation() bool {
	fmt.Fprint(u.stdout, color.YellowString("Are you sure you want to proceed with uninstallation? [y/N]: "))
	var response string
	_, _ = fmt.Scanln(&response)
	response = strings.ToLower(strings.TrimSpace(response))
	return response == "y" || response == "yes"
}

// runCommand executes a command and returns the output.
func (u *uninstallOrchestrator) runCommand(ctx context.Context, name string, args ...string) (string, error) {
	if u.cmd.DryRun {
		u.logger.Info("Dry run: would execute command", "command", name, "args", args)
		return "", nil
	}

	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), fmt.Errorf("command failed: %s %v: %w\nOutput: %s", name, args, err, string(output))
	}
	return string(output), nil
}

// checkInstalledComponents checks what AI Gateway components are installed.
func (u *uninstallOrchestrator) checkInstalledComponents(ctx context.Context) ([]string, error) {
	u.printStep("🔍", "Checking installed components...")

	var components []string

	// Check AI Gateway controller.
	if u.isHelmReleaseInstalled(ctx, "aieg", u.cmd.Namespace) {
		components = append(components, "ai-gateway-controller")
	}

	// Check AI Gateway CRDs.
	if u.isHelmReleaseInstalled(ctx, "aieg-crd", u.cmd.Namespace) {
		components = append(components, "ai-gateway-crds")
	}

	// Check Redis.
	if u.isRedisInstalled(ctx) {
		components = append(components, "redis")
	}

	// Check Envoy Gateway configuration.
	if u.isEnvoyGatewayConfigInstalled(ctx) {
		components = append(components, "envoy-gateway-config")
	}

	// Check Envoy Gateway.
	if u.isHelmReleaseInstalled(ctx, "eg", "envoy-gateway-system") {
		components = append(components, "envoy-gateway")
	}

	if len(components) > 0 {
		u.printInfo(fmt.Sprintf("Found components: %s", strings.Join(components, ", ")))
	} else {
		u.printInfo("No AI Gateway components found")
	}

	return components, nil
}

// isHelmReleaseInstalled checks if a Helm release is installed.
func (u *uninstallOrchestrator) isHelmReleaseInstalled(ctx context.Context, releaseName, namespace string) bool {
	args := []string{"list", "-n", namespace, "-f", releaseName, "--short"}

	output, err := u.runCommand(ctx, "helm", args...)
	if err != nil {
		u.logger.Debug("Failed to check Helm release", "release", releaseName, "error", err)
		return false
	}

	return strings.TrimSpace(output) != ""
}

// isRedisInstalled checks if Redis is installed.
func (u *uninstallOrchestrator) isRedisInstalled(ctx context.Context) bool {
	args := []string{
		"get", "namespace", "redis-system",
		"--ignore-not-found=true",
		"--no-headers",
	}

	output, err := u.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return false
	}

	return strings.TrimSpace(output) != ""
}

// isEnvoyGatewayConfigInstalled checks if Envoy Gateway configuration is installed.
func (u *uninstallOrchestrator) isEnvoyGatewayConfigInstalled(ctx context.Context) bool {
	// Check for AI Gateway specific RBAC resources.
	args := []string{
		"get", "clusterrole", "ai-gateway-role",
		"--ignore-not-found=true",
		"--no-headers",
	}

	output, err := u.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return false
	}

	return strings.TrimSpace(output) != ""
}

// removeAIGatewayResources removes AI Gateway resources.
func (u *uninstallOrchestrator) removeAIGatewayResources(ctx context.Context, components []string) error {
	u.printStep("🗑️", "Removing AI Gateway resources...")

	for _, component := range components {
		switch component {
		case "ai-gateway-controller":
			if err := u.uninstallHelmRelease(ctx, "aieg", u.cmd.Namespace); err != nil {
				return fmt.Errorf("failed to uninstall AI Gateway controller: %w", err)
			}
		case "ai-gateway-crds":
			if err := u.uninstallHelmRelease(ctx, "aieg-crd", u.cmd.Namespace); err != nil {
				return fmt.Errorf("failed to uninstall AI Gateway CRDs: %w", err)
			}
		case "redis":
			if err := u.removeRedis(ctx); err != nil {
				return fmt.Errorf("failed to remove Redis: %w", err)
			}
		}
	}

	u.printSuccess("AI Gateway resources removed")
	return nil
}

// removeEnvoyGatewayConfig removes Envoy Gateway configuration.
func (u *uninstallOrchestrator) removeEnvoyGatewayConfig(ctx context.Context) error {
	u.printStep("⚙️", "Removing Envoy Gateway configuration...")

	// Remove RBAC configuration.
	if err := u.removeYAMLFromFile(ctx, rbacConfigURL); err != nil {
		u.printWarning(fmt.Sprintf("Failed to remove RBAC configuration: %v", err))
	}

	u.printSuccess("Envoy Gateway configuration removed")
	return nil
}

// removeEnvoyGateway removes Envoy Gateway.
func (u *uninstallOrchestrator) removeEnvoyGateway(ctx context.Context) error {
	u.printStep("🌐", "Removing Envoy Gateway...")

	if err := u.uninstallHelmRelease(ctx, "eg", "envoy-gateway-system"); err != nil {
		return fmt.Errorf("failed to uninstall Envoy Gateway: %w", err)
	}

	u.printSuccess("Envoy Gateway removed")
	return nil
}

// verifyCleanup verifies that components have been removed.
func (u *uninstallOrchestrator) verifyCleanup(ctx context.Context) error {
	u.printStep("🔍", "Verifying cleanup...")

	// Check if any components are still installed.
	components, err := u.checkInstalledComponents(ctx)
	if err != nil {
		return err
	}

	if len(components) > 0 {
		u.printWarning(fmt.Sprintf("Some components may still be present: %s", strings.Join(components, ", ")))
	} else {
		u.printSuccess("All components successfully removed")
	}

	return nil
}

// uninstallHelmRelease uninstalls a Helm release.
func (u *uninstallOrchestrator) uninstallHelmRelease(ctx context.Context, releaseName, namespace string) error {
	u.printStep("  📦", fmt.Sprintf("Uninstalling Helm release %s...", releaseName))

	args := []string{"uninstall", releaseName, "-n", namespace}

	if u.cmd.DryRun {
		args = append(args, "--dry-run")
	}

	output, err := u.runCommand(ctx, "helm", args...)
	if err != nil {
		// Don't fail if release doesn't exist.
		if strings.Contains(err.Error(), "not found") {
			u.printInfo(fmt.Sprintf("Helm release %s not found (already removed)", releaseName))
			return nil
		}
		return err
	}

	if !u.cmd.DryRun {
		u.logger.Info("Helm release uninstalled", "release", releaseName, "output", output)
	}

	u.printSuccess(fmt.Sprintf("Helm release %s uninstalled", releaseName))
	return nil
}

// removeRedis removes Redis resources.
func (u *uninstallOrchestrator) removeRedis(ctx context.Context) error {
	u.printStep("  🗄️", "Removing Redis...")

	if err := u.removeYAMLFromFile(ctx, redisConfigURL); err != nil {
		return err
	}

	u.printSuccess("Redis removed")
	return nil
}

// removeYAMLFromFile removes resources defined in a YAML file.
func (u *uninstallOrchestrator) removeYAMLFromFile(ctx context.Context, filePath string) error {
	args := []string{"delete", "-f", filePath, "--ignore-not-found=true"}

	if u.cmd.DryRun {
		args = append(args, "--dry-run=client")
	}

	output, err := u.runCommand(ctx, "kubectl", args...)
	if err != nil {
		return fmt.Errorf("failed to remove %s: %w", filePath, err)
	}

	if !u.cmd.DryRun {
		u.logger.Info("Removed YAML file", "file", filePath, "output", output)
	}

	return nil
}
